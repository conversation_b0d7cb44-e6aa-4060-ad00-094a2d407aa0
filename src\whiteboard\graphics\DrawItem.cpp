#include "DrawItem.h"
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>
#include <QApplication>
#include <QDateTime>
#include <QPixmap>
#include <QPainter>
#include <QFileInfo>

DrawItem::DrawItem(const QPainterPath& path, const QPen& pen, const QBrush& brush,
                   ToolType type, QGraphicsItem* parent)
    : QGraphicsItem(parent)
    , m_path(path)
    , m_pen(pen)
    , m_brush(brush)
    , m_toolType(type)
    , m_timestamp(QDateTime::currentMSecsSinceEpoch())
    , m_itemId(generateUniqueId())
    , m_maxHeight(800)
    , m_boundingRectValid(false)
{
    m_pixmapTransform = QTransform();
    m_path_transform = QTransform();
}

// 图片构造函数
DrawItem::DrawItem(const QString& imagePath, const QPointF& position,
                   qreal displayWidth, qreal maxHeight, QGraphicsItem* parent)
    : QGraphicsItem(parent)
    , m_path(QPainterPath())  // 图片类型不使用路径
    , m_pen(QPen())
    , m_brush(QBrush())
    , m_toolType(ToolType::Image)
    , m_timestamp(QDateTime::currentMSecsSinceEpoch())
    , m_itemId(generateUniqueId())
    , m_imagePath(imagePath)
    , m_displaySize(0, 0)
    , m_maxHeight(maxHeight > 0 ? maxHeight : 800)
    , m_boundingRectValid(false)
{
//    setPos(position);
    m_pixmapTransform = QTransform();
    m_pixmapTransform.translate(position.x(), position.y());

    m_path_transform = QTransform();

    // 加载图片
    if (!loadImage(imagePath, displayWidth, maxHeight)) {
        qWarning() << "DrawItem: 无法加载图片" << imagePath;
    }
}

void DrawItem::paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget)
{
    Q_UNUSED(option)
    Q_UNUSED(widget)

    // 如果是图片类型，绘制图片
    if (m_toolType == ToolType::Image) {
        if (m_pixmap.isNull()) {
            // 绘制占位符
            painter->setPen(QPen(Qt::gray, 2, Qt::DashLine));
            painter->setBrush(QBrush(Qt::lightGray, Qt::DiagCrossPattern));
            painter->drawRect(boundingRect());

            painter->setPen(Qt::black);
            painter->drawText(boundingRect(), Qt::AlignCenter, "图片加载失败");
            return;
        }

        // 设置高质量渲染
        painter->setRenderHint(QPainter::Antialiasing, true);
        painter->setRenderHint(QPainter::SmoothPixmapTransform, true);

        // 绘制图片
        QRectF targetRect(0, 0, m_displaySize.width(), m_displaySize.height());
        QRectF sourceRect = m_originalPixmap.rect();
        sourceRect = m_pixmapTransform.mapRect(sourceRect);
        painter->drawPixmap(sourceRect, m_pixmap, m_pixmap.rect());
        return;
    }

    // 普通图形绘制
    if (m_path.isEmpty()) {
        return;
    }

    painter->setRenderHint(QPainter::Antialiasing, true);
    FeatheringRenderer::drawPathWithFeathering(painter, m_path, m_pen, m_brush, m_toolType);

    
}

QRectF DrawItem::boundingRect() const
{
    if (!m_boundingRectValid) {
        m_cachedBoundingRect = calculateBoundingRect();
        m_boundingRectValid = true;
    }
    return m_cachedBoundingRect;
}

QPainterPath DrawItem::shape() const
{
    if (m_toolType == ToolType::Image) {
        QPainterPath path;
        path.addRect(boundingRect());
        return path;
    }
    return m_path;
}

// 属性设置方法
void DrawItem::setPen(const QPen& pen)
{
    if (m_pen != pen) {
        m_pen = pen;
        invalidateBoundingRect();
        update();
    }
}

void DrawItem::setBrush(const QBrush& brush)
{
    if (m_brush != brush) {
        m_brush = brush;
        update();
    }
}


void DrawItem::updatePath(const QPainterPath& newPath)
{
    if (m_path != newPath) {
        prepareGeometryChange();
        m_path = newPath;
        invalidateBoundingRect();
    }
}

void DrawItem::bakeTransform(const QTransform& transform, const QPointF& position)
{
    Q_UNUSED(position) // position不应该烘焙到几何数据中，应该保持为图形的位置

    // 如果是图片类型，需要特殊处理
    if (m_toolType == ToolType::Image) {
        // 对于图片，我们需要更新显示尺寸和位置
        // 这里简化处理，主要针对路径图形
        prepareGeometryChange();

        m_pixmapTransform = m_pixmapTransform * transform;

        m_pixmap = m_originalPixmap.transformed(m_pixmapTransform);

        invalidateBoundingRect();
        return;
    }

    // 只将transform（旋转、缩放）应用到路径几何数据中
    if (!transform.isIdentity()) {
        prepareGeometryChange();

        // 只应用transform，不包括position
        m_path = transform.map(m_path);
        m_path_transform = m_path_transform * transform;

        invalidateBoundingRect();
    }
}
// 序列化支持
QJsonObject DrawItem::toJson() const
{
    QJsonObject json;
    json["itemId"] = m_itemId;
    json["type"] = static_cast<int>(m_toolType);
    json["timestamp"] = m_timestamp;

    // 画笔属性
    QJsonObject penObj;

    // 安全地获取颜色信息（包含透明度）
    QColor penColor = m_pen.color();
    if (penColor.isValid()) {
        // 使用RGBA格式保存颜色，确保透明度信息不丢失
        penObj["color"] = penColor.name(QColor::HexArgb);
        penObj["alpha"] = penColor.alpha(); // 额外保存alpha值作为备份
    } else {
        penObj["color"] = "#FF000000"; // 默认黑色，完全不透明
        penObj["alpha"] = 255;
        qWarning() << "DrawItem::toJson: 无效的画笔颜色，使用默认黑色";
    }

    penObj["width"] = m_pen.widthF();
    penObj["style"] = static_cast<int>(m_pen.style());
    penObj["capStyle"] = static_cast<int>(m_pen.capStyle());
    penObj["joinStyle"] = static_cast<int>(m_pen.joinStyle());

    // 保存自定义虚线模式
    if (m_pen.style() == Qt::CustomDashLine) {
        QVector<qreal> dashPattern = m_pen.dashPattern();
        QJsonArray dashArray;
        for (qreal dash : dashPattern) {
            dashArray.append(dash);
        }
        penObj["dashPattern"] = dashArray;
    }

    json["pen"] = penObj;

    // 画刷属性
    QJsonObject brushObj;

    // 安全地获取画刷颜色信息（包含透明度）
    QColor brushColor = m_brush.color();
    if (brushColor.isValid()) {
        // 使用RGBA格式保存颜色，确保透明度信息不丢失
        brushObj["color"] = brushColor.name(QColor::HexArgb);
        brushObj["alpha"] = brushColor.alpha(); // 额外保存alpha值作为备份
    } else {
        brushObj["color"] = "#FF000000"; // 默认黑色，完全不透明
        brushObj["alpha"] = 255;
        qWarning() << "DrawItem::toJson: 无效的画刷颜色，使用默认黑色";
    }

    brushObj["style"] = static_cast<int>(m_brush.style());
    json["brush"] = brushObj;

    // 路径数据（完整存储）
    QJsonObject pathObj;
    QRectF bounds = m_path.boundingRect();
    pathObj["bounds"] = QString("%1,%2,%3,%4").arg(bounds.x()).arg(bounds.y()).arg(bounds.width()).arg(bounds.height());

    QJsonArray elementsArray;
    for (int i = 0; i < m_path.elementCount(); ++i) {
        QPainterPath::Element element = m_path.elementAt(i);
        QJsonObject elementObj;
        elementObj["x"] = element.x;
        elementObj["y"] = element.y;
        elementObj["type"] = static_cast<int>(element.type);
        elementsArray.append(elementObj);
    }
    pathObj["elements"] = elementsArray;
    json["path"] = pathObj;
    json["pathTransform"] = serializeTransform(m_path_transform);

    // 如果是图片类型，添加图片相关数据
    if (m_toolType == ToolType::Image) {
        json["imagePath"] = m_imagePath;

        QJsonObject sizeObj;
        sizeObj["width"] = m_displaySize.width();
        sizeObj["height"] = m_displaySize.height();
        json["displaySize"] = sizeObj;

        json["maxHeight"] = m_maxHeight;

        json["pixmapTransform"] = serializeTransform(m_pixmapTransform);
    }

    return json;
}

void DrawItem::fromJson(const QJsonObject& json)
{
    // 基本属性
    m_itemId = json["itemId"].toString();
    if (m_itemId.isEmpty()) {
        m_itemId = generateUniqueId(); // 为旧数据生成新ID
    }
    m_toolType = static_cast<ToolType>(json["type"].toInt());
    m_timestamp = json["timestamp"].toVariant().toLongLong();

    // 恢复画笔
    QJsonObject penObj = json["pen"].toObject();

    // 恢复颜色（支持透明度）
    QString colorStr = penObj["color"].toString();
    QColor penColor(colorStr);

    // 如果颜色字符串不包含alpha信息，尝试从alpha字段恢复
    if (!colorStr.startsWith("#FF") && !colorStr.startsWith("#ff") && penObj.contains("alpha")) {
        penColor.setAlpha(penObj["alpha"].toInt());
    }

    m_pen.setColor(penColor);
    m_pen.setWidthF(penObj["width"].toDouble());
    m_pen.setStyle(static_cast<Qt::PenStyle>(penObj["style"].toInt()));

    // 恢复线帽和连接样式，如果没有保存则使用抗锯齿默认值
    if (penObj.contains("capStyle")) {
        m_pen.setCapStyle(static_cast<Qt::PenCapStyle>(penObj["capStyle"].toInt()));
    } else {
        m_pen.setCapStyle(Qt::RoundCap);  // 默认使用圆形线帽
    }

    if (penObj.contains("joinStyle")) {
        m_pen.setJoinStyle(static_cast<Qt::PenJoinStyle>(penObj["joinStyle"].toInt()));
    } else {
        m_pen.setJoinStyle(Qt::RoundJoin);  // 默认使用圆形连接
    }

    // 恢复自定义虚线模式
    if (m_pen.style() == Qt::CustomDashLine && penObj.contains("dashPattern")) {
        QJsonArray dashArray = penObj["dashPattern"].toArray();
        QVector<qreal> dashPattern;
        for (const QJsonValue& value : dashArray) {
            dashPattern.append(value.toDouble());
        }
        m_pen.setDashPattern(dashPattern);
    }

    // 恢复画刷
    QJsonObject brushObj = json["brush"].toObject();

    // 恢复颜色（支持透明度）
    QString brushColorStr = brushObj["color"].toString();
    QColor brushColor(brushColorStr);

    // 如果颜色字符串不包含alpha信息，尝试从alpha字段恢复
    if (!brushColorStr.startsWith("#FF") && !brushColorStr.startsWith("#ff") && brushObj.contains("alpha")) {
        brushColor.setAlpha(brushObj["alpha"].toInt());
    }

    m_brush.setColor(brushColor);
    m_brush.setStyle(static_cast<Qt::BrushStyle>(brushObj["style"].toInt()));

    if (json.contains("path")) {
        QJsonObject pathObj = json["path"].toObject();
        if (pathObj.contains("elements")) {
            QJsonArray elementsArray = pathObj["elements"].toArray();
            QPainterPath newPath;

            for (int i = 0; i < elementsArray.size(); ++i) {
                QJsonObject elementObj = elementsArray[i].toObject();
                qreal x = elementObj["x"].toDouble();
                qreal y = elementObj["y"].toDouble();
                QPainterPath::ElementType type = static_cast<QPainterPath::ElementType>(elementObj["type"].toInt());

                switch (type) {
                case QPainterPath::MoveToElement:
                    newPath.moveTo(x, y);
                    break;
                case QPainterPath::LineToElement:
                    newPath.lineTo(x, y);
                    break;
                case QPainterPath::CurveToElement:
                    // 对于曲线，需要下两个点作为控制点
                    if (elementsArray.size() > i + 2) {
                        QJsonObject cp1Obj = elementsArray[i + 1].toObject();
                        QJsonObject cp2Obj = elementsArray[i + 2].toObject();
                        newPath.cubicTo(x, y, cp1Obj["x"].toDouble(), cp1Obj["y"].toDouble(),
                                       cp2Obj["x"].toDouble(), cp2Obj["y"].toDouble());
                        i += 2; // 跳过已处理的控制点
                    }
                    break;
                default:
                    break;
                }
            }

            m_path = newPath;
        }
    }
    if (json.contains("pathTransform")) {
        m_path_transform = deserializeTransform(json["pathTransform"].toString());
    }

    // 如果是图片类型，恢复图片数据
    if (m_toolType == ToolType::Image) {
        m_imagePath = json["imagePath"].toString();

        QJsonObject sizeObj = json["displaySize"].toObject();
        m_displaySize = QSizeF(sizeObj["width"].toDouble(), sizeObj["height"].toDouble());

        m_maxHeight = json["maxHeight"].toDouble();
        if (json.contains("pixmapTransform")) {
            m_pixmapTransform = deserializeTransform(json["pixmapTransform"].toString());
        }

        // 重新加载图片
        if (!m_imagePath.isEmpty()) {
            if (!m_originalPixmap.load(m_imagePath)) {
                qWarning() << "DrawItem::fromJson: 无法重新加载图片" << m_imagePath;
            }
            m_pixmap = m_originalPixmap.transformed(m_pixmapTransform, Qt::SmoothTransformation);

        }
    }

    invalidateBoundingRect();
}

// 内存使用估算
qint64 DrawItem::memoryUsage() const
{
    qint64 usage = sizeof(*this);

    // 估算路径的内存使用
    usage += m_path.elementCount() * sizeof(QPainterPath::Element);

    return usage;
}

// 类型名称
QString DrawItem::typeName() const
{
    switch (m_toolType) {
    case ToolType::FreeDraw: return "FreeDraw";
    case ToolType::FreeDrawDashed: return "FreeDrawDashed";
    case ToolType::FreeDrawHighlighter: return "FreeDrawHighlighter";
    case ToolType::Line: return "Line";
    case ToolType::DashedLine: return "DashedLine";
    case ToolType::Rectangle: return "Rectangle";
    case ToolType::Square: return "Square";
    case ToolType::Ellipse: return "Ellipse";
    case ToolType::Circle: return "Circle";
    case ToolType::Triangle: return "Triangle";
    case ToolType::RightTriangle: return "RightTriangle";
    case ToolType::Arrow: return "Arrow";
    case ToolType::Image: return "Image";
    case ToolType::Eraser: return "Eraser";
    case ToolType::Lasso: return "Lasso";
    case ToolType::PassThrough: return "PassThrough";
    default: return "Unknown";
    }
}

// 私有辅助方法
void DrawItem::invalidateBoundingRect()
{
    m_boundingRectValid = false;
}

QRectF DrawItem::calculateBoundingRect() const
{
    // 如果是图片类型，返回图片的边界矩形
    if (m_toolType == ToolType::Image) {
        qDebug() << "DrawItem::calculateBoundingRect: ImageItem:" << m_pixmap.rect() << ", m_path: " << m_path.boundingRect();
        QRectF sourceRect = m_originalPixmap.rect();
        sourceRect = m_pixmapTransform.mapRect(sourceRect);
        return sourceRect;
//        return m_path.boundingRect();
    }

    // 普通图形的边界矩形计算
    if (m_path.isEmpty()) {
        return QRectF(0, 0, 1, 1);
    }

    QRectF bounds = m_path.boundingRect();

    if (bounds.width() == 0 && bounds.height() == 0 && m_path.elementCount() > 0) {
        QPainterPath::Element firstElement = m_path.elementAt(0);
        qreal penWidth = qMax(m_pen.widthF(), 2.0);
        bounds = QRectF(firstElement.x - penWidth/2, firstElement.y - penWidth/2, penWidth, penWidth);
    }

    // 考虑画笔宽度
    qreal penWidth = m_pen.widthF();
    if (penWidth > 0) {
        qreal halfPen = penWidth / 2.0;
        bounds.adjust(-halfPen, -halfPen, halfPen, halfPen);
    }

    return bounds;
}

// ID管理方法
void DrawItem::setItemId(const QString& id)
{
    m_itemId = id;
}

QString DrawItem::generateUniqueId()
{
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

// 图片相关方法实现
bool DrawItem::loadImage(const QString& imagePath, qreal displayWidth, qreal maxHeight)
{
    if (imagePath.isEmpty()) {
        return false;
    }

    // 检查文件是否存在
    QFileInfo fileInfo(imagePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        qWarning() << "DrawItem: 图片文件不存在:" << imagePath;
        return false;
    }

    // 加载图片
    if (!m_originalPixmap.load(imagePath)) {
        qWarning() << "DrawItem: 无法加载图片:" << imagePath;
        return false;
    }

    m_imagePath = imagePath;

    // 计算显示尺寸
    QSizeF originalSize = m_originalPixmap.size();
    qreal targetWidth = displayWidth > 0 ? displayWidth : 400;
    qreal targetMaxHeight = maxHeight > 0 ? maxHeight : 800;

    // 按宽度缩放
    qreal scale = targetWidth / originalSize.width();
    qreal scaledHeight = originalSize.height() * scale;

    // 如果高度超过限制，按高度重新缩放
    if (scaledHeight > targetMaxHeight) {
        scale = targetMaxHeight / originalSize.height();
        targetWidth = originalSize.width() * scale;
        scaledHeight = targetMaxHeight;
    }

    m_displaySize = QSizeF(targetWidth, scaledHeight);
    m_maxHeight = targetMaxHeight;



    m_pixmapTransform.scale(scale, scale);

    m_pixmap = m_originalPixmap.transformed(m_pixmapTransform, Qt::SmoothTransformation);

    // 使边界矩形失效，强制重新计算
    invalidateBoundingRect();
    update();

    qDebug() << "DrawItem: 图片加载成功" << imagePath
             << "原始尺寸:" << originalSize
             << "显示尺寸:" << m_displaySize;

    return true;
}



QString DrawItem::serializeTransform(QTransform transform) const {
    // 序列化变换矩阵为字符串
    QString transformString;
    QTextStream stream(&transformString);
    stream << transform.m11() << "," << transform.m12() << "," << transform.m13() << ","
           << transform.m21() << "," << transform.m22() << "," << transform.m23() << ","
           << transform.m31() << "," << transform.m32() << "," << transform.m33();
    return transformString;
}

QTransform DrawItem::deserializeTransform(const QString &transformStr) const {
    // 从字符串反序列化变换矩阵
    QStringList values = transformStr.split(",");
    if (values.size() == 9) {
        QTransform transform;
        transform.setMatrix(
            values[0].toDouble(), values[1].toDouble(), values[2].toDouble(),
            values[3].toDouble(), values[4].toDouble(), values[5].toDouble(),
            values[6].toDouble(), values[7].toDouble(), values[8].toDouble()
        );
        return transform;
    }
    return QTransform(); // 返回单位矩阵作为默认值
}
DrawItem::~DrawItem() {
    qDebug() << "DrawItem destroyed: " << m_imagePath << ", itemId: " << m_itemId;
}

bool DrawItem::has90Degrees() {
// 1. 提取原始变换中的旋转角度（以度为单位）
    qreal rotationAngle = m_path_transform.map(QLineF(0, 0, 1, 0)).angleTo(QLineF(0, 0, 1, 0));

    // 2. 检查角度是否为90度或270度
    return (qFuzzyCompare(qAbs(rotationAngle), 0) || qFuzzyCompare(qAbs(rotationAngle), 90.0) || qFuzzyCompare(qAbs(rotationAngle), 270.0));
}

DrawItem::LineDirection DrawItem::lineDirection() {
    QPainterPath path = m_path;
    if (path.elementCount() < 2) {
        return DirectSlash;
    }
    // 斜向右上角：x2 > x1 且 y2 < y1（X 增大，Y 减小）
    // 斜向右下角：x2 > x1 且 y2 > y1（X 增大，Y 增大）
    QPainterPath::Element firstElement = path.elementAt(0);
    QPainterPath::Element lastElement = path.elementAt(1);

    if (lastElement.x > firstElement.x && lastElement.y < firstElement.y) {
        return DirectSlash;
    }
    else if (lastElement.x < firstElement.x && lastElement.y > firstElement.y) {
        return DirectSlash;
    }
    else if (lastElement.x > firstElement.x && lastElement.y > firstElement.y) {
        return ReverseSlash;
    }
    else if (lastElement.x < firstElement.x && lastElement.y < firstElement.y) {
        return ReverseSlash;
    }
    else {
        return DirectSlash;
    }
}

QPointF DrawItem::lineStartPoint() {
    if (m_toolType != ToolType::DashedLine && m_toolType != ToolType::Line && m_toolType != ToolType::Arrow) {
        qDebug() << "DrawItem: lineStartPoint() only support Line, Arrow, DashedLine";
        return QPointF();
    }
    return QPointF(m_path.elementAt(0).x, m_path.elementAt(0).y);
}



QPointF DrawItem::lineEndPoint() {
    if (m_toolType != ToolType::DashedLine && m_toolType != ToolType::Line && m_toolType != ToolType::Arrow) {
        qDebug() << "DrawItem: lineEndPoint() only support Line, Arrow, DashedLine";
        return QPointF();
    }
    if (m_path.elementCount() < 2) {
        qDebug() << "DrawItem: lineEndPoint() path.elementCount() < 2";
        return QPointF();
    }
    if (m_toolType == ToolType::DashedLine) {
        return QPointF(m_path.elementAt(m_path.elementCount() - 1).x, m_path.elementAt(m_path.elementCount() - 1).y);
    }
    else {
        return QPointF(m_path.elementAt(1).x, m_path.elementAt(1).y);
    }
}



void DrawItem::setPath(QPainterPath path) {
    if (m_toolType != ToolType::DashedLine && m_toolType != ToolType::Line && m_toolType != ToolType::Arrow) {
        qDebug() << "DrawItem: setPath() only support Line, Arrow, DashedLine";
    }
    m_path = path;
    invalidateBoundingRect();
}
